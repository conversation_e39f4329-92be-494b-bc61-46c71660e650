{% load static %}
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>{% block title %}African Recipe finder{% endblock %}</title>

    <!-- CSS Styles -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Consolidated Recipe Finder CSS -->
    <link rel="stylesheet" href="{% static 'css/base.css' %}">
    <link rel="stylesheet" href="{% static 'css/components.css' %}">
    <link rel="stylesheet" href="{% static 'css/pages.css' %}">

    {% block extra_css %}{% endblock %}

    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- HTMX -->
    <script src="{% static 'js/htmx.min.js' %}"></script>

    <!-- Alpine.js -->
    <script src="{% static 'js/alpine.min.js' %}" defer></script>
</head>

<body class="home-page d-flex flex-column min-vh-100">
    <!-- Top Navigation Bar (shown after search) -->


    <!-- Mobile Hamburger Menu
    <div class="mobile-hamburger" onclick="toggleMobileNav()">
        <img src="{% static 'images/logos/hamburger-menu.webp' %}" alt="Menu" class="hamburger-icon">
    </div> -->

    <!-- Mobile Navigation Overlay -->
    <div class="mobile-nav-overlay" onclick="closeMobileNav()"></div>

  

    <div class="body-with-sidebar">
        <div class="sidebar">
            <!-- Logo at top of sidebar -->
            <div class="sidebar-logo">
                <img src="{% static 'images/logos/Recipe-Finder-logo.webp' %}" alt="Recipe Finder" class="sidebar-logo-img">
            </div>
            <!-- New button below logo -->
            <div class="sidebar-new-button">
                <a href="#" class="sidebar-new-link" title="Create New Recipe">
                    <i class="bi bi-plus-circle"></i>
                    <span>New</span>
                </a>
            </div>
            <ul class="sidebar-nav">
                <li class="sidebar-item">
                    <a href="{% url 'recipes:home' %}"
                        class="sidebar-link {% if request.resolver_match.url_name == 'home' %}active{% endif %}">
                        <i class="bi bi-house"></i>
                        <span>Home</span>
                    </a>
                </li>
                <li class="sidebar-item">
                    <a href="{% url 'recipes:discover' %}"
                        class="sidebar-link {% if request.resolver_match.url_name == 'discover' %}active{% endif %}">
                        <i class="bi bi-compass"></i>
                        <span>Discover</span>
                    </a>
                </li>
                <li class="sidebar-item">
                    <a href="#" class="sidebar-link" title="Bookmarks">
                        <i class="bi bi-bookmark"></i>
                        <span>Bookmarks</span>
                    </a>
                </li>
                <li class="sidebar-item">
                    <a href="#" class="sidebar-link" title="Favourites">
                        <i class="bi bi-heart"></i>
                        <span>Favourites</span>
                    </a>
                </li>
            </ul>
        </div>
        <div class="d-flex flex-column flex-grow-1 w-100">
            <main class="main-content flex-grow-1">
                {% block content %}{% endblock %}
                <div class="top-nav-bar" id="top-nav-bar" style="display: none;">
                    <div class="top-nav-content">
                        <h1 class="top-nav-title">My Recipe Finder</h1>
                    </div>
                </div>
            </main>
            
            <footer class="bg-light mt-auto">
                <div class="container text-center">
                    <p>&copy; {% now "Y" %} Recipe finder. All rights reserved.</p>
                </div>
            </footer>
        </div>
    </div>

    <!-- Mobile Bottom Navigation (inspired by mobile-inspiration.html) -->
    <div class="mobile-bottom-nav" id="mobileBottomNav">
        <div class="mobile-nav-container">
            <div class="mobile-nav-item {% if request.resolver_match.url_name == 'home' %}active{% endif %}" data-page="home">
                <svg class="mobile-nav-icon home-icon" viewBox="0 0 24 24">
                    <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                    <polyline points="9,22 9,12 15,12 15,22"></polyline>
                </svg>
                <span class="mobile-nav-label">Home</span>
            </div>

            <div class="mobile-nav-item mobile-nav-new" data-page="new">
                <svg class="mobile-nav-icon plus-icon" viewBox="0 0 24 24">
                    <line x1="12" y1="5" x2="12" y2="19"></line>
                    <line x1="5" y1="12" x2="19" y2="12"></line>
                </svg>
                <span class="mobile-nav-label">New</span>
            </div>

            <div class="mobile-nav-item" data-page="bookmarks">
                <svg class="mobile-nav-icon bookmark-icon" viewBox="0 0 24 24">
                    <path d="m19 21-7-4-7 4V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v16z"></path>
                </svg>
                <span class="mobile-nav-label">Bookmarks</span>
            </div>

            <div class="mobile-nav-item" data-page="favourites">
                <svg class="mobile-nav-icon heart-icon" viewBox="0 0 24 24">
                    <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
                </svg>
                <span class="mobile-nav-label">Favourites</span>
            </div>

            <div class="mobile-nav-item" data-page="history">
                <svg class="mobile-nav-icon history-icon" viewBox="0 0 24 24">
                    <path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"></path>
                    <path d="M3 3v5h5"></path>
                    <polyline points="12,7 12,12 16,16"></polyline>
                </svg>
                <span class="mobile-nav-label">History</span>
            </div>

            <div class="mobile-nav-item {% if request.resolver_match.url_name == 'discover' %}active{% endif %}" data-page="discover">
                <i class="bi bi-compass mobile-nav-icon"></i>
                <span class="mobile-nav-label">Discover</span>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JavaScript -->
    <script src="{% static 'js/main.js' %}"></script>

    <!-- Mobile Interactions -->
    <script src="{% static 'js/mobile-interactions.js' %}"></script>

    <!-- Mobile Navigation JavaScript -->
    <script>
        function toggleMobileNav() {
            const menu = document.getElementById('mobileNavMenu');
            const overlay = document.querySelector('.mobile-nav-overlay');

            if (menu.classList.contains('open')) {
                closeMobileNav();
            } else {
                menu.classList.add('open');
                overlay.style.display = 'block';
                document.body.style.overflow = 'hidden';
            }
        }

        function closeMobileNav() {
            const menu = document.getElementById('mobileNavMenu');
            const overlay = document.querySelector('.mobile-nav-overlay');

            menu.classList.remove('open');
            overlay.style.display = 'none';
            document.body.style.overflow = '';
        }

        // Close menu when clicking on a nav link
        document.addEventListener('DOMContentLoaded', function() {
            const navLinks = document.querySelectorAll('.mobile-nav-menu .nav-link');
            navLinks.forEach(link => {
                link.addEventListener('click', closeMobileNav);
            });
        });

        // Mobile Bottom Navigation Scroll Behavior (inspired by mobile-inspiration.html)
        let lastScrollTop = 0;
        const mobileBottomNav = document.getElementById('mobileBottomNav');
        const mobileHamburger = document.querySelector('.mobile-hamburger');
        const mobileNavItems = document.querySelectorAll('.mobile-nav-item');

        // Scroll behavior for hiding/showing navigation
        window.addEventListener('scroll', function() {
            // Only apply scroll behavior on mobile screens
            if (window.innerWidth > 768) return;

            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

            if (scrollTop > lastScrollTop && scrollTop > 60) {
                // Scrolling down - hide navigation
                if (mobileBottomNav) mobileBottomNav.classList.add('hidden');
                if (mobileHamburger) mobileHamburger.classList.add('hidden');
            } else if (scrollTop < lastScrollTop) {
                // Scrolling up - show navigation
                if (mobileBottomNav) mobileBottomNav.classList.remove('hidden');
                if (mobileHamburger) mobileHamburger.classList.remove('hidden');
            }

            lastScrollTop = scrollTop <= 0 ? 0 : scrollTop;
        });

        // Mobile Bottom Navigation item click handlers
        mobileNavItems.forEach(item => {
            item.addEventListener('click', function() {
                // Remove active class from all items
                mobileNavItems.forEach(nav => nav.classList.remove('active'));

                // Add active class to clicked item (except for new button)
                if (!this.classList.contains('mobile-nav-new')) {
                    this.classList.add('active');
                }

                // Handle different navigation actions
                const page = this.getAttribute('data-page');
                switch(page) {
                    case 'home':
                        window.location.href = "{% url 'recipes:home' %}";
                        break;
                    case 'new':
                        // Navigate to fresh home page (root URL)
                        window.location.href = "{% url 'recipes:home' %}";

                        // Add a subtle bounce animation
                        this.style.transform = 'translateX(-50%) translateY(-2px) scale(0.95)';
                        setTimeout(() => {
                            this.style.transform = 'translateX(-50%) translateY(-2px) scale(1)';
                        }, 150);
                        break;
                    case 'bookmarks':
                        // TODO: Implement bookmarks functionality
                        console.log('Bookmarks feature coming soon!');
                        break;
                    case 'favourites':
                        // TODO: Implement favourites functionality
                        console.log('Favourites feature coming soon!');
                        break;
                    case 'history':
                        window.location.href = "{% url 'recipes:search_history' %}";
                        break;
                    case 'discover':
                        window.location.href = "{% url 'recipes:discover' %}";
                        break;
                }
            });
        });

        // Add touch feedback for mobile bottom navigation
        mobileNavItems.forEach(item => {
            item.addEventListener('touchstart', function() {
                this.style.transform = this.classList.contains('mobile-nav-new') ?
                    'translateX(-50%) translateY(-2px) scale(0.95)' : 'scale(0.95)';
            });

            item.addEventListener('touchend', function() {
                this.style.transform = this.classList.contains('mobile-nav-new') ?
                    'translateX(-50%) translateY(-2px) scale(1)' : 'scale(1)';
            });
        });






    </script>

    {% block extra_js %}{% endblock %}
</body>

</html>